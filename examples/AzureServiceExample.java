package examples;

import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.providers.AzureService;
import com.xiaozhi.dialogue.stt.providers.AzureSttService;
import com.xiaozhi.entity.SysConfig;
import reactor.core.publisher.Sinks;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Azure Cognitive Services 使用示例
 * 
 * 本示例展示如何使用Azure TTS和STT服务
 * 
 * 使用前请确保：
 * 1. 已在Azure Portal创建语音服务资源
 * 2. 获取了有效的订阅密钥和服务区域
 * 3. 设置了正确的配置参数
 */
public class AzureServiceExample {

    public static void main(String[] args) {
        // 示例配置 - 请替换为您的实际配置
        String subscriptionKey = "your-azure-subscription-key";
        String region = "eastus"; // 或您的服务区域
        String outputPath = "audio-output";
        
        // 创建输出目录
        new File(outputPath).mkdirs();
        
        // 演示TTS服务
        demonstrateTTS(subscriptionKey, region, outputPath);
        
        // 演示STT服务
        demonstrateSTT(subscriptionKey, region);
    }
    
    /**
     * 演示Azure TTS服务使用
     */
    private static void demonstrateTTS(String subscriptionKey, String region, String outputPath) {
        System.out.println("=== Azure TTS 服务演示 ===");
        
        try {
            // 创建TTS配置
            SysConfig ttsConfig = new SysConfig();
            ttsConfig.setProvider("azure");
            ttsConfig.setApiKey(subscriptionKey);
            ttsConfig.setApiSecret(region);
            
            // 创建Azure TTS服务实例
            AzureService azureService = new AzureService(ttsConfig, "zh-CN-XiaoxiaoNeural", outputPath);
            
            System.out.println("服务提供商: " + azureService.getProviderName());
            System.out.println("音频格式: " + azureService.audioFormat());
            System.out.println("支持流式TTS: " + azureService.isSupportStreamTts());
            
            // 1. 基本文本转语音
            System.out.println("\n1. 基本文本转语音");
            Text2SpeechParams params = new Text2SpeechParams("你好，欢迎使用Azure语音服务！", 1.0);
            String audioFilePath = azureService.textToSpeech(params);
            
            if (audioFilePath != null && Files.exists(Paths.get(audioFilePath))) {
                long fileSize = Files.size(Paths.get(audioFilePath));
                System.out.println("✓ 音频文件生成成功: " + audioFilePath);
                System.out.println("  文件大小: " + fileSize + " 字节");
            } else {
                System.out.println("✗ 音频文件生成失败");
            }
            
            // 2. 不同语速的文本转语音
            System.out.println("\n2. 不同语速测试");
            double[] speeds = {0.5, 1.0, 1.5, 2.0};
            for (double speed : speeds) {
                Text2SpeechParams speedParams = new Text2SpeechParams(
                    "这是语速" + speed + "倍的测试", speed);
                String speedAudioPath = azureService.textToSpeech(speedParams);
                if (speedAudioPath != null) {
                    System.out.println("✓ 语速 " + speed + "x 音频生成成功");
                }
            }
            
            // 3. 流式TTS演示
            System.out.println("\n3. 流式TTS演示");
            final int[] chunkCount = {0};
            final int[] totalBytes = {0};
            
            azureService.streamTextToSpeech("这是流式语音合成的演示，音频数据将实时返回。", audioData -> {
                chunkCount[0]++;
                totalBytes[0] += audioData.length;
                System.out.println("  收到音频块 " + chunkCount[0] + ": " + audioData.length + " 字节");
            });
            
            System.out.println("✓ 流式TTS完成，共收到 " + chunkCount[0] + " 个音频块，总计 " + totalBytes[0] + " 字节");
            
        } catch (Exception e) {
            System.err.println("✗ TTS演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示Azure STT服务使用
     */
    private static void demonstrateSTT(String subscriptionKey, String region) {
        System.out.println("\n=== Azure STT 服务演示 ===");
        
        try {
            // 创建STT配置
            SysConfig sttConfig = new SysConfig();
            sttConfig.setProvider("azure");
            sttConfig.setApiKey(subscriptionKey);
            sttConfig.setApiSecret(region);
            sttConfig.setApiUrl("zh-CN"); // 识别语言
            
            // 创建Azure STT服务实例
            AzureSttService azureService = new AzureSttService(sttConfig);
            
            System.out.println("服务提供商: " + azureService.getProviderName());
            System.out.println("支持流式STT: " + azureService.supportsStreaming());
            
            // 1. 单次识别演示（使用虚拟音频数据）
            System.out.println("\n1. 单次识别演示");
            byte[] dummyAudioData = generateDummyAudioData(16000); // 1秒的虚拟音频数据
            String recognizedText = azureService.recognition(dummyAudioData);
            
            if (recognizedText != null && !recognizedText.isEmpty()) {
                System.out.println("✓ 识别结果: " + recognizedText);
            } else {
                System.out.println("○ 未识别到语音内容（使用的是虚拟音频数据）");
            }
            
            // 2. 流式识别演示
            System.out.println("\n2. 流式识别演示");
            Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
            
            // 在后台线程中模拟音频数据流
            Thread.startVirtualThread(() -> {
                try {
                    System.out.println("  开始发送音频数据流...");
                    
                    // 模拟发送多个音频块
                    for (int i = 0; i < 5; i++) {
                        byte[] audioChunk = generateDummyAudioData(3200); // 0.2秒的音频块
                        audioSink.tryEmitNext(audioChunk);
                        System.out.println("  发送音频块 " + (i + 1));
                        Thread.sleep(200); // 模拟实时音频流
                    }
                    
                    System.out.println("  音频数据流发送完成");
                    audioSink.tryEmitComplete();
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.err.println("  音频数据流发送被中断");
                }
            });
            
            // 执行流式识别
            String streamResult = azureService.streamRecognition(audioSink);
            
            if (streamResult != null && !streamResult.isEmpty()) {
                System.out.println("✓ 流式识别结果: " + streamResult);
            } else {
                System.out.println("○ 流式识别完成（使用的是虚拟音频数据，可能无识别结果）");
            }
            
        } catch (Exception e) {
            System.err.println("✗ STT演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成虚拟音频数据用于演示
     * 注意：这只是用于演示的虚拟数据，实际使用时应该使用真实的音频数据
     */
    private static byte[] generateDummyAudioData(int samples) {
        byte[] audioData = new byte[samples * 2]; // 16位音频，每个样本2字节
        
        // 生成简单的正弦波音频数据
        for (int i = 0; i < samples; i++) {
            double angle = 2.0 * Math.PI * 440.0 * i / 16000.0; // 440Hz正弦波
            short sample = (short) (Short.MAX_VALUE * 0.1 * Math.sin(angle)); // 10%音量
            
            // 将16位样本转换为字节（小端序）
            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return audioData;
    }
    
    /**
     * 支持的语音列表演示
     */
    private static void demonstrateVoices() {
        System.out.println("\n=== 支持的语音示例 ===");
        
        String[] chineseVoices = {
            "zh-CN-XiaoxiaoNeural",  // 晓晓（女声）
            "zh-CN-YunxiNeural",     // 云希（男声）
            "zh-CN-YunyangNeural",   // 云扬（男声）
            "zh-CN-XiaochenNeural",  // 晓辰（女声）
            "zh-CN-XiaohanNeural",   // 晓涵（女声）
            "zh-CN-XiaomengNeural",  // 晓梦（女声）
            "zh-CN-XiaomoNeural",    // 晓墨（女声）
            "zh-CN-XiaoqiuNeural",   // 晓秋（女声）
            "zh-CN-XiaoruiNeural",   // 晓睿（女声）
            "zh-CN-XiaoshuangNeural", // 晓双（女声）
            "zh-CN-XiaoyanNeural",   // 晓颜（女声）
            "zh-CN-XiaoyouNeural",   // 晓悠（女声）
            "zh-CN-XiaozhenNeural",  // 晓甄（女声）
            "zh-CN-YunjianNeural",   // 云健（男声）
            "zh-CN-YunxiaNeural",    // 云夏（男声）
            "zh-CN-YunyeNeural"      // 云野（男声）
        };
        
        System.out.println("中文语音:");
        for (String voice : chineseVoices) {
            System.out.println("  " + voice);
        }
        
        String[] englishVoices = {
            "en-US-JennyNeural",     // Jenny（女声）
            "en-US-GuyNeural",       // Guy（男声）
            "en-US-AriaNeural",      // Aria（女声）
            "en-US-DavisNeural",     // Davis（男声）
            "en-US-AmberNeural",     // Amber（女声）
            "en-US-AnaNeural",       // Ana（女声）
            "en-US-AshleyNeural",    // Ashley（女声）
            "en-US-BrandonNeural",   // Brandon（男声）
            "en-US-ChristopherNeural", // Christopher（男声）
            "en-US-CoraNeural",      // Cora（女声）
            "en-US-ElizabethNeural", // Elizabeth（女声）
            "en-US-EricNeural",      // Eric（男声）
            "en-US-JacobNeural",     // Jacob（男声）
            "en-US-JaneNeural",      // Jane（女声）
            "en-US-JasonNeural",     // Jason（男声）
            "en-US-MichelleNeural",  // Michelle（女声）
            "en-US-MonicaNeural",    // Monica（女声）
            "en-US-NancyNeural",     // Nancy（女声）
            "en-US-RogerNeural",     // Roger（男声）
            "en-US-SaraNeural",      // Sara（女声）
            "en-US-SteffanNeural",   // Steffan（男声）
            "en-US-TonyNeural"       // Tony（男声）
        };
        
        System.out.println("\n英文语音:");
        for (String voice : englishVoices) {
            System.out.println("  " + voice);
        }
    }
}
