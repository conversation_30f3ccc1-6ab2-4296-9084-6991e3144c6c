package examples;

import com.xiaozhi.dialogue.llm.memory.SummaryChatMemory;
import com.xiaozhi.entity.SysMessage;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * SummaryChatMemory 使用示例
 * 
 * 这个示例展示了如何使用 SummaryChatMemory 进行智能记忆管理
 */
public class SummaryChatMemoryExample {
    
    public static void main(String[] args) {
        // 创建 SummaryChatMemory 实例
        RestTemplate restTemplate = new RestTemplate();
        String openMemoryBaseUrl = "http://localhost:8765";
        boolean enabled = true;
        
        SummaryChatMemory chatMemory = new SummaryChatMemory(restTemplate, openMemoryBaseUrl, enabled);
        
        // 示例设备和会话信息
        String deviceId = "device_12345";
        String sessionId = "session_67890";
        Integer roleId = 1;
        
        // 1. 添加用户消息
        System.out.println("添加用户消息...");
        chatMemory.addMessage(
            deviceId, 
            sessionId, 
            "user", 
            "你好，我想了解一下今天的天气情况", 
            roleId, 
            SysMessage.MESSAGE_TYPE_NORMAL, 
            System.currentTimeMillis()
        );
        
        // 2. 添加助手回复
        System.out.println("添加助手回复...");
        chatMemory.addMessage(
            deviceId, 
            sessionId, 
            "assistant", 
            "您好！今天是晴天，温度大约在25度左右，适合外出活动。", 
            roleId, 
            SysMessage.MESSAGE_TYPE_NORMAL, 
            System.currentTimeMillis()
        );
        
        // 3. 添加更多对话
        System.out.println("添加更多对话...");
        chatMemory.addMessage(
            deviceId, 
            sessionId, 
            "user", 
            "那明天呢？", 
            roleId, 
            SysMessage.MESSAGE_TYPE_NORMAL, 
            System.currentTimeMillis()
        );
        
        chatMemory.addMessage(
            deviceId, 
            sessionId, 
            "assistant", 
            "明天预计会有小雨，建议您带上雨伞。", 
            roleId, 
            SysMessage.MESSAGE_TYPE_NORMAL, 
            System.currentTimeMillis()
        );
        
        // 等待异步操作完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 4. 获取历史消息
        System.out.println("获取历史消息...");
        List<SysMessage> messages = chatMemory.getMessages(deviceId, SysMessage.MESSAGE_TYPE_NORMAL, 10);
        
        System.out.println("找到 " + messages.size() + " 条历史消息:");
        for (SysMessage message : messages) {
            System.out.println("- [" + message.getSender() + "] " + message.getContent());
        }
        
        // 5. 清除设备记忆（可选）
        System.out.println("清除设备记忆...");
        // chatMemory.clearMessages(deviceId);
        
        System.out.println("示例完成！");
    }
}
