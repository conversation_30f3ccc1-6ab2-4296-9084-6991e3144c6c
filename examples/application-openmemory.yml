# OpenMemory 配置示例
# 将此配置添加到您的 application.yml 文件中

# OpenMemory 配置
openmemory:
  # OpenMemory 服务的基础 URL
  base-url: http://localhost:8765
  
  # 是否启用 SummaryChatMemory
  # 设置为 false 将使用默认的 DatabaseChatMemory
  enabled: true
  
  # HTTP 请求超时时间（毫秒）
  timeout: 30000

# 可选：如果您需要自定义日志级别
logging:
  level:
    com.xiaozhi.dialogue.llm.memory.SummaryChatMemory: DEBUG
    
# 可选：如果您需要配置 HTTP 连接池
server:
  tomcat:
    threads:
      max: 200
      min-spare: 10
