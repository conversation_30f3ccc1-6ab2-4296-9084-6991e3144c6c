package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.entity.SysConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class YoudaoServiceTest {

    private YoudaoService youdaoService;
    private SysConfig config;

    @BeforeEach
    void setUp() {
        config = new SysConfig();
        config.setAppId("test-app-id");
        config.setApiSecret("test-app-secret");
        config.setApiUrl("wss://openapi.youdao.com/stream_asropenapi");
        
        youdaoService = new YoudaoService(config);
    }

    @Test
    void testGetProviderName() {
        assertEquals("youdao", youdaoService.getProviderName());
    }

    @Test
    void testSupportsStreaming() {
        assertTrue(youdaoService.supportsStreaming());
    }

    @Test
    void testRecognition() {
        // 单次识别应该返回null并记录警告
        byte[] audioData = new byte[1024];
        String result = youdaoService.recognition(audioData);
        assertNull(result);
    }

    @Test
    void testStreamRecognitionWithNullConfig() {
        // 测试配置为空的情况
        SysConfig nullConfig = new SysConfig();
        YoudaoService serviceWithNullConfig = new YoudaoService(nullConfig);
        
        // 这里应该返回空字符串，因为配置不完整
        // 实际的流式识别测试需要真实的API密钥和网络连接
        // 所以这里只测试配置验证逻辑
        assertNotNull(serviceWithNullConfig);
        assertEquals("youdao", serviceWithNullConfig.getProviderName());
    }

    @Test
    void testDefaultApiUrl() {
        SysConfig configWithoutUrl = new SysConfig();
        configWithoutUrl.setAppId("test-app-id");
        configWithoutUrl.setApiSecret("test-app-secret");
        // 不设置 apiUrl
        
        YoudaoService serviceWithDefaultUrl = new YoudaoService(configWithoutUrl);
        assertNotNull(serviceWithDefaultUrl);
        assertEquals("youdao", serviceWithDefaultUrl.getProviderName());
    }
}
