package com.xiaozhi.enums;

import lombok.Getter;

/**
 * 授权类型
 */
@Getter
public enum AuthType {
    Unknown(0),
    <PERSON><PERSON>(1),
    <PERSON><PERSON>(2),
    Manager(3);

    private final int value;

    AuthType(int value) {
        this.value = value;
    }

    public static AuthType of(Integer num) {
        for (var val : values()) {
            if (val.value == num) {
                return val;
            }
        }
        return AuthType.Unknown;
    }
}
