package com.xiaozhi.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum TaskStatus {
    Waiting(0), // 待执行
    Running(1), // 执行中
    Interrupted(2), // 中断执行
    Finished(3), // 任务完成
    Canceled(4); // 任务取消

    @JsonValue
    @EnumValue
    private final int value;

    TaskStatus(int value) {
        this.value = value;
    }
}
