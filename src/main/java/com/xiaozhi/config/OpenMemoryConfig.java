package com.xiaozhi.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * OpenMemory 配置类
 * 配置与OpenMemory API通信的相关组件
 */
@Configuration
@ConfigurationProperties(prefix = "openmemory")
public class OpenMemoryConfig {
    private static final Logger logger = LoggerFactory.getLogger(OpenMemoryConfig.class);
    
    @Value("${openmemory.base-url:http://localhost:8765}")
    private String baseUrl;
    
    @Value("${openmemory.timeout:30000}")
    private int timeout;
    
    @Value("${openmemory.enabled:true}")
    private boolean enabled;
    
    /**
     * 配置用于OpenMemory API调用的RestTemplate
     */
    @Bean("openMemoryRestTemplate")
    public RestTemplate openMemoryRestTemplate(RestTemplateBuilder builder) {
        logger.info("Configuring OpenMemory RestTemplate with base URL: {}, timeout: {}ms, enabled: {}", 
                   baseUrl, timeout, enabled);
        
        return builder
                .setConnectTimeout(Duration.ofMillis(timeout))
                .setReadTimeout(Duration.ofMillis(timeout))
                .requestFactory(this::createRequestFactory)
                .build();
    }
    
    /**
     * 创建HTTP请求工厂
     */
    private ClientHttpRequestFactory createRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(timeout);
        factory.setReadTimeout(timeout);
        return factory;
    }
}
