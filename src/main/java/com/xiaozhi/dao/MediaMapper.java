package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.xiaozhi.dto.MediaInfo;
import com.xiaozhi.entity.Media;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface MediaMapper extends BaseMapper<Media> {

    @Select("""
            SELECT m.id, m.title, m.category, cover.file_url AS cover_url, asset.file_url AS asset_url FROM medias m
            JOIN file_resources AS cover ON cover.id = m.cover_id AND cover.is_deleted = 0
            JOIN file_resources AS asset ON asset.id = m.asset_id AND asset.is_deleted = 0
            ${ew.customSqlSegment}
            """)
    IPage<MediaInfo> findPage(Page<MediaInfo> page, @Param(Constants.WRAPPER) Wrapper<MediaInfo> query);

}
