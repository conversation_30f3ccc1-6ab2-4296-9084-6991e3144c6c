package com.xiaozhi.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.dto.MessageDuration;
import com.xiaozhi.entity.SysMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Select;

/**
 * 聊天记录 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface MessageMapper extends BaseMapper<SysMessage> {

    @Select("SELECT MAX(id) AS id, MIN(created_at) AS begin, MAX(created_at) AS end FROM sys_message ${ew.customSqlSegment}")
    MessageDuration findDuration(@Param(Constants.WRAPPER) Wrapper<SysMessage> wrapper);

}