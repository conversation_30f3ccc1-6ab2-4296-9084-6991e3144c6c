package com.xiaozhi.dialogue.tts.providers;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.microsoft.cognitiveservices.speech.audio.AudioOutputStream;
import com.microsoft.cognitiveservices.speech.audio.PushAudioOutputStreamCallback;
import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.entity.SysConfig;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

import java.net.URI;
import java.nio.file.Paths;
import java.util.concurrent.ExecutionException;
import java.util.function.Consumer;

@Slf4j
public class AzureService implements TtsService {
    private static final String PROVIDER_NAME = "azure";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    private final String url;
    private final String secret;
    private final String voice;
    private final String outputPath;

    public AzureService(SysConfig config, String voice, String outputPath) {
        this.url = config.getApiUrl();
        this.secret = config.getApiSecret(); // 使用 apiSecret 存储 region
        this.voice = voice;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "wav";
    }

    @Override
    public boolean isSupportStreamTts() {
        return true;
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        if (params.getText() == null || params.getText().isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        var filepath = Paths.get(outputPath, getAudioFileName()).toString();

        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                synthesizeToFile(params.getText(), filepath, params.getSpeed());
                return filepath;
            } catch (Exception e) {
                attempts++;
                log.warn("Azure TTS 第{}次尝试失败: {}", attempts, e.getMessage());

                if (attempts >= MAX_RETRY_ATTEMPTS) {
                    log.error("Azure TTS 语音合成失败，已重试{}次", MAX_RETRY_ATTEMPTS, e);
                    throw new Exception("Azure TTS 语音合成失败: " + e.getMessage(), e);
                }

                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("Azure TTS 重试被中断", ie);
                }
            }
        }

        return filepath;
    }

    private void synthesizeToFile(String text, String filepath, double speed) throws Exception {
        try (var speechConfig = SpeechConfig.fromEndpoint(new URI(url), secret);
             var audioConfig = AudioConfig.fromWavFileOutput(filepath)) {

            speechConfig.setSpeechSynthesisVoiceName(voice);
            speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm);

            // 构建SSML以支持语速调节
            var ssml = buildSSML("en-US", text, speed);

            try (var synthesizer = new SpeechSynthesizer(speechConfig, audioConfig)) {
                var result = synthesizer.SpeakSsmlAsync(ssml).get();

                if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                    log.debug("Azure TTS 合成完成，文件保存至: {}", filepath);
                } else if (result.getReason() == ResultReason.Canceled) {
                    var cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                    log.error("Azure TTS 合成被取消: {}", cancellation.getReason());
                    if (cancellation.getReason() == CancellationReason.Error) {
                        log.error("Azure TTS 错误代码: {}, 错误详情: {}",
                                cancellation.getErrorCode(), cancellation.getErrorDetails());
                        throw new Exception("Azure TTS 合成失败: " + cancellation.getErrorDetails());
                    }
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure TTS 合成异常", e);
            throw new Exception("Azure TTS 合成异常: " + e.getMessage(), e);
        }
    }

    @Override
    public Sinks.Many<byte[]> streamTextToSpeech(String text) {
        if (text == null || text.isEmpty()) {
            log.warn("文本内容为空！");
            return null;
        }

        // 创建音频数据流
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

        // 在后台线程中执行流式TTS
        Thread.startVirtualThread(() -> {
            try {
                streamSynthesis(text, audioSink, 1.0); // 默认语速
            } catch (Exception e) {
                log.error("Azure 流式TTS合成失败", e);
                audioSink.tryEmitError(e);
            }
        });

        return audioSink;
    }

    private void streamSynthesis(String text, Sinks.Many<byte[]> audioSink, double speed) throws Exception {
        try (var speechConfig = SpeechConfig.fromEndpoint(new URI(url), secret)) {
            speechConfig.setSpeechSynthesisVoiceName(voice);
            speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Audio24Khz16Bit24KbpsMonoOpus);

            // 创建推送音频输出流回调
            var callback = new PushAudioOutputStreamCallback() {
                @Override
                public int write(byte[] dataBuffer) {
                    if (dataBuffer != null && dataBuffer.length > 0) {
                        // 发送音频数据到流
                        audioSink.tryEmitNext(dataBuffer.clone());
                        log.info("发送音频数据块: {} 字节", dataBuffer.length);
                    }
                    return dataBuffer != null ? dataBuffer.length : 0;
                }

                @Override
                public void close() {
                    log.info("音频流关闭");
                    audioSink.tryEmitComplete();
                }
            };

            // 创建推送音频输出流
            AudioOutputStream pushStream = AudioOutputStream.createPushStream(callback);
            AudioConfig audioConfig = AudioConfig.fromStreamOutput(pushStream);

            // 构建SSML以支持语速调节
            var ssml = buildSSML("en-US", text, speed);

            try (var synthesizer = new SpeechSynthesizer(speechConfig, audioConfig)) {
                // 开始异步合成
                var result = synthesizer.SpeakSsmlAsync(ssml).get();

                if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                    log.info("Azure 流式TTS合成完成");
                } else if (result.getReason() == ResultReason.Canceled) {
                    var cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                    log.error("Azure 流式TTS合成被取消: {}", cancellation.getReason());
                    if (cancellation.getReason() == CancellationReason.Error) {
                        log.error("Azure TTS 错误代码: {}, 错误详情: {}",
                                cancellation.getErrorCode(), cancellation.getErrorDetails());
                        throw new Exception("Azure 流式TTS合成失败: " + cancellation.getErrorDetails());
                    }
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure 流式TTS合成异常", e);
            throw new Exception("Azure 流式TTS合成异常: " + e.getMessage(), e);
        }
    }

    private String buildSSML(String lang, String text, double speed) {
        // 将速度转换为百分比格式，Azure TTS 支持的范围是 0.5x 到 2.0x
        double clampedSpeed = Math.max(0.5, Math.min(2.0, speed));
        String speedPercent = String.format("%.0f%%", clampedSpeed * 100);

        return STR."""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="\{lang}">
                <voice name="\{voice}">
                    <prosody rate="\{speedPercent}">\{text}</prosody>
                </voice>
            </speak>""";
    }

}
