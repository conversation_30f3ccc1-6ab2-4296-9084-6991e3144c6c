package com.xiaozhi.dialogue.tts;

import reactor.core.publisher.Sinks;

import java.util.UUID;
import java.util.function.Consumer;

/**
 * TTS服务接口
 */
public interface TtsService {

  /**
   * 获取服务提供商名称
   */
  String getProviderName();

  /**
   * 音频格式
   */
  default String audioFormat() {
    return "wav";
  }

  /**
   * 生成文件名称
   * 
   * @return 文件名称
   */
  default String getAudioFileName() {
    return UUID.randomUUID().toString().replace("-", "") + "." + audioFormat();
  }

  /**
   * 
   */
  default boolean isSupportStreamTts() {
    return false;
  }

  /**
   * 将文本转换为语音（带自定义语音）
   *
   * @param params@return 生成的音频文件路径
   */
  String textToSpeech(Text2SpeechParams params) throws Exception;

  /**
   * 流式将文本转换为语音
   * 
   * @param text              要转换为语音的文本
   */
  default Sinks.Many<byte[]> streamTextToSpeech(String text) {
    throw new UnsupportedOperationException("Unimplemented method 'streamTextToSpeech'");
  }
}
