package com.xiaozhi.dialogue.llm;

import com.xiaozhi.dto.ChatParams;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

public class ChatModelService {

    private ChatModel chatModel;

    public ChatModelService() {

    }

    public ChatModelService(ChatModel model) {
        this.chatModel = model;
    }

    public String call(String message) {
        return chatModel.call(message);
    }

    public String call(Message... message) {
        return chatModel.call(message);
    }

    public ChatResponse call(Prompt prompt) {
        return chatModel.call(prompt);
    }

    public Flux<ChatResponse> stream(Prompt prompt) {
        return chatModel.stream(prompt);
    }

    public String getConversationId(ChatParams params) {
        throw new UnsupportedOperationException();
    }

    public Flux<ChatResponse> stream(Prompt prompt, ChatParams params) {
        return chatModel.stream(prompt);
    }
}
