package com.xiaozhi.dialogue.llm.tool.function;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.ToolCallStringResultConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class SessionExitFunction implements ToolsGlobalRegistry.GlobalFunction {

    ToolCallback toolCallback = FunctionToolCallback
            .builder("func_stop_chat", (Map<String, String> _, ToolContext toolContext) -> {
                var chatSession = (ChatSession) toolContext.getContext().get(ChatService.TOOL_CONTEXT_SESSION_KEY);
                chatSession.setCloseAfterChat(true);
                return "拜拜~下次再聊哦";
            })
            .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
            .description("Provide exit system function, call this tool when the user intends to leave")
            .inputSchema("""
                        {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    """)
            .inputType(Map.class)
            .toolCallResultConverter(ToolCallStringResultConverter.INSTANCE)
            .build();

    @Override
    public ToolCallback getFunctionCallTool(ChatSession chatSession) {
        return null;
    }
}
