package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ChatMemoryMapper;
import com.xiaozhi.entity.SysChatMemory;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于OpenMemory的聊天记忆实现
 * 使用OpenMemory API进行智能记忆管理和摘要生成
 * 只有在配置启用时才会激活此实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "xiaozhi.memory.type", havingValue = "summary")
public class SummaryChatMemory implements ChatMemory {
    @Resource
    private ChatMemoryMapper chatMemoryMapper;

    @Override
    public void addMessage(String deviceId, String sessionId, String sender, String content, Integer roleId, String messageType, Long timeMillis) {
        if (!sender.equals("summary")) return;

        JsonUtil.parse(content, FactResult.class)
                .map(FactResult::getFacts)
                .map(facts -> {
                    var memory = new SysChatMemory()
                            .setDeviceId(deviceId)
                            .setFacts(JsonUtil.toJson(facts));
                    chatMemoryMapper.insert(memory);
                    return true;
                });
    }

    @Override
    public List<SysMessage> getMessages(String deviceId, String messageType, Integer limit) {
        var query = new OhMyLambdaQueryWrapper<SysChatMemory>()
                .eq(SysChatMemory::getDeviceId, deviceId)
                .orderByDesc(SysChatMemory::getId)
                .last(STR."limit \{limit}");

        var memories = chatMemoryMapper.selectList(query);

        log.info("Use memory {}", memories);

        return memories.stream()
                .map(it -> new SysMessage()
                        .setContent(it.getFacts())
                        .setDeviceId(deviceId)
                        .setSender("summary")
                        .setType("summary"))
                .collect(Collectors.toList());
    }

    @Override
    public void clearMessages(String deviceId) {

    }

    @Data
    public static class FactResult {
        private List<String> facts;
    }
}
