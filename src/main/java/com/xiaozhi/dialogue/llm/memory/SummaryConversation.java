package com.xiaozhi.dialogue.llm.memory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.dialogue.llm.ChatModelService;
import com.xiaozhi.dialogue.llm.Prompts;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SummaryConversation extends Conversation {

    private int remain = 20;

    private final ChatMemory chatMemory;

    private final ChatModelService chatModel;

    public SummaryConversation(SysDevice device, SysRole role, String sessionId, ChatMemory chatMemory, ChatModelService chatModel) {
        super(device, role, sessionId);

        this.chatModel = chatModel;
        this.chatMemory = chatMemory;
    }

    @Override
    public void addMessage(UserMessage userMessage, Long userTimeMillis, AssistantMessage assistantMessage, Long assistantTimeMillis) {
        super.addMessage(userMessage, userTimeMillis, assistantMessage, assistantTimeMillis);
        if (remain == 0) {
            var input = messages.stream()
                    .map(msg -> switch (msg) {
                        case UserMessage m -> STR."user: \{m.getText()}";
                        case AssistantMessage m -> STR."assistant: \{m.getText()}";
                        default -> "";
                    })
                    .collect(Collectors.joining("\n"));
            log.info("Fact input {}", input);
            var response = chatModel.call(
                    new SystemMessage(Prompts.FACT_RETRIEVAL_PROMPT),
                    new UserMessage(STR."Input:\n\{input}")
            );

            log.info("FACTS {}", response);

            chatMemory.addMessage(device().getDeviceId(), sessionId(), "summary", response, role().getId(), "summary", assistantTimeMillis);

            remain += 20;
            messages.removeFirst();
            messages.removeFirst();
        }
        remain -= 2;
    }

    @Override
    public List<Message> prompt(UserMessage userMessage) {
        var summaries = chatMemory.getMessages(device().getDeviceId(), "summary", 10)
                .stream()
                .flatMap(it -> JsonUtil.parse(it.getContent(), new TypeReference<List<String>>() {}).getOrElse(List.of()).stream())
                .distinct()
                .map(it -> STR."- \{it}")
                .collect(Collectors.joining("\n"));


        var intro = STR."""
                \{role().getIntro()}
                User's previous summary:
                \{summaries}
                """;
        var systemMessage = new SystemMessage(intro);

        var messages = new ArrayList<Message>();
        messages.add(systemMessage);
        messages.addAll(this.messages);
        messages.add(userMessage);

        return messages;
    }
}
