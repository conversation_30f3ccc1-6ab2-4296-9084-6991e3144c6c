package com.xiaozhi.dialogue.domain;

import lombok.Data;
import reactor.core.publisher.Sinks;

import java.util.List;

/**
 * 句子对象，用于跟踪每个句子的处理状态
 */
@Data
public class Sentence {
    private int seq;
    private final String text;
    private boolean isFirst;
    private boolean isLast;
    private boolean ready = false;
    private String audioPath = null;
    private List<byte[]> audioChunks = null; // 流式音频数据块
    private Sinks.Many<byte[]> streamingAudioSink = null; // 流式音频流
    private long timestamp = System.currentTimeMillis();
    private double modelResponseTime = 0.0; // 模型响应时间（秒）
    private double ttsGenerationTime = 0.0; // TTS生成时间（秒）
    private Long assistantTimeMillis = null; // 对话ID
    private List<String> moods;

    public Sentence(String text) {
        this.text = text;
    }

    public Sentence(String text, String audioPath) {
        this.text = text;
        this.audioPath = audioPath;
    }

    public Sentence(int seq, String text, boolean isFirst, boolean isLast) {
        this.seq = seq;
        this.text = text;
        this.isFirst = isFirst;
        this.isLast = isLast;
    }

    public void setAudio(String path) {
        this.audioPath = path;
        this.ready = true;
    }

    public void setAudioChunks(List<byte[]> chunks) {
        this.audioChunks = chunks;
        this.ready = true;
    }

    public void setStreamingAudioSink(Sinks.Many<byte[]> sink) {
        this.streamingAudioSink = sink;
        this.ready = true;
    }

    public boolean hasStreamingAudio() {
        return streamingAudioSink != null;
    }

    public boolean isTimeout() {
        return System.currentTimeMillis() - timestamp > 5000; // 5秒超时
    }
}
