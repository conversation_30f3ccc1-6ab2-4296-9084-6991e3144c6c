package com.xiaozhi.dialogue.stt.providers;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import com.microsoft.cognitiveservices.speech.audio.AudioInputStream;
import com.microsoft.cognitiveservices.speech.audio.PushAudioInputStream;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.utils.AudioUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

import java.io.ByteArrayInputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class AzureSttService implements SttService {
    private static final String PROVIDER_NAME = "azure";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;
    private static final long RECOGNITION_TIMEOUT_MS = 30000; // 30秒超时

    private final String subscriptionKey;
    private final String region;
    private final String language;

    public AzureSttService(SysConfig config) {
        this.subscriptionKey = config.getApiKey();
        this.region = config.getApiSecret(); // 使用 apiSecret 存储 region
        this.language = config.getApiUrl() != null ? config.getApiUrl() : "zh-CN"; // 使用 apiUrl 存储语言，默认中文
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String recognition(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            log.warn("音频数据为空！");
            return null;
        }

        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                return recognizeFromBytes(audioData);
            } catch (Exception e) {
                attempts++;
                log.warn("Azure STT 第{}次尝试失败: {}", attempts, e.getMessage());
                
                if (attempts >= MAX_RETRY_ATTEMPTS) {
                    log.error("Azure STT 语音识别失败，已重试{}次", MAX_RETRY_ATTEMPTS, e);
                    return null;
                }
                
                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
        }
        
        return null;
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        var finalResult = new AtomicReference<>("");
        var recognitionLatch = new CountDownLatch(1);

        try (SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region)) {
            speechConfig.setSpeechRecognitionLanguage(language);
            
            // 创建推送音频输入流
            PushAudioInputStream pushStream = AudioInputStream.createPushStream();
            AudioConfig audioConfig = AudioConfig.fromStreamInput(pushStream);

            try (SpeechRecognizer recognizer = new SpeechRecognizer(speechConfig, audioConfig)) {
                
                // 设置识别事件监听器
                recognizer.recognizing.addEventListener((s, e) -> {
                    log.debug("Azure STT 识别中: {}", e.getResult().getText());
                });

                recognizer.recognized.addEventListener((s, e) -> {
                    if (e.getResult().getReason() == ResultReason.RecognizedSpeech) {
                        String recognizedText = e.getResult().getText();
                        if (recognizedText != null && !recognizedText.trim().isEmpty()) {
                            log.info("Azure STT 识别结果: {}", recognizedText);
                            finalResult.set(recognizedText);
                        }
                    } else if (e.getResult().getReason() == ResultReason.NoMatch) {
                        log.debug("Azure STT 未识别到语音");
                    }
                });

                recognizer.canceled.addEventListener((s, e) -> {
                    log.error("Azure STT 识别被取消: {}", e.getReason());
                    if (e.getReason() == CancellationReason.Error) {
                        log.error("Azure STT 错误代码: {}, 错误详情: {}", 
                            e.getErrorCode(), e.getErrorDetails());
                    }
                    recognitionLatch.countDown();
                });

                recognizer.sessionStopped.addEventListener((s, e) -> {
                    log.debug("Azure STT 会话结束");
                    recognitionLatch.countDown();
                });

                // 开始连续识别
                recognizer.startContinuousRecognitionAsync().get();

                // 订阅音频流并推送数据
                audioSink.asFlux().subscribe(
                    audioChunk -> {
                        try {
                            pushStream.write(audioChunk);
                        } catch (Exception e) {
                            log.error("推送音频数据失败", e);
                        }
                    },
                    error -> {
                        log.error("音频流错误", error);
                        try {
                            pushStream.close();
                        } catch (Exception e) {
                            log.error("关闭音频流失败", e);
                        }
                        recognitionLatch.countDown();
                    },
                    () -> {
                        log.debug("音频流结束");
                        try {
                            pushStream.close();
                        } catch (Exception e) {
                            log.error("关闭音频流失败", e);
                        }
                        // 给一些时间让最后的识别结果返回
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        recognitionLatch.countDown();
                    }
                );

                // 等待识别完成或超时
                boolean recognized = recognitionLatch.await(RECOGNITION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                
                if (!recognized) {
                    log.warn("Azure STT 识别超时");
                }

                // 停止识别
                recognizer.stopContinuousRecognitionAsync().get();
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure STT 流式识别异常", e);
        } catch (Exception e) {
            log.error("Azure STT 初始化失败", e);
        }

        return finalResult.get();
    }

    private String recognizeFromBytes(byte[] audioData) throws Exception {
        try (SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region)) {
            speechConfig.setSpeechRecognitionLanguage(language);
            
            // 创建音频输入流
            ByteArrayInputStream audioStream = new ByteArrayInputStream(audioData);
            PushAudioInputStream pushStream = AudioInputStream.createPushStream();
            AudioConfig audioConfig = AudioConfig.fromStreamInput(pushStream);

            try (SpeechRecognizer recognizer = new SpeechRecognizer(speechConfig, audioConfig)) {
                
                // 推送音频数据
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = audioStream.read(buffer)) != -1) {
                    if (bytesRead == buffer.length) {
                        pushStream.write(buffer);
                    } else {
                        // 如果读取的字节数少于缓冲区大小，创建一个新的数组
                        byte[] actualData = new byte[bytesRead];
                        System.arraycopy(buffer, 0, actualData, 0, bytesRead);
                        pushStream.write(actualData);
                    }
                }
                pushStream.close();

                // 执行识别
                SpeechRecognitionResult result = recognizer.recognizeOnceAsync().get();
                
                if (result.getReason() == ResultReason.RecognizedSpeech) {
                    log.debug("Azure STT 识别完成: {}", result.getText());
                    return result.getText();
                } else if (result.getReason() == ResultReason.NoMatch) {
                    log.debug("Azure STT 未识别到语音");
                    return "";
                } else if (result.getReason() == ResultReason.Canceled) {
                    CancellationDetails cancellation = CancellationDetails.fromResult(result);
                    log.error("Azure STT 识别被取消: {}", cancellation.getReason());
                    if (cancellation.getReason() == CancellationReason.Error) {
                        log.error("Azure STT 错误代码: {}, 错误详情: {}", 
                            cancellation.getErrorCode(), cancellation.getErrorDetails());
                        throw new Exception("Azure STT 识别失败: " + cancellation.getErrorDetails());
                    }
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Azure STT 识别异常", e);
            throw new Exception("Azure STT 识别异常: " + e.getMessage(), e);
        }
        
        return "";
    }
}
