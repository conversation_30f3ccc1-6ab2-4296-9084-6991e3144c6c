package com.xiaozhi.dialogue.stt.factory;

import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.dialogue.stt.providers.*;
import com.xiaozhi.entity.SysConfig;

import jakarta.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SttServiceFactory {

    private static final Logger logger = LoggerFactory.getLogger(SttServiceFactory.class);

    // 缓存已初始化的服务：key format: "provider:configId"
    private final Map<String, SttService> serviceCache = new ConcurrentHashMap<>();

    // 默认服务提供商名称
    private static final String DEFAULT_PROVIDER = "vosk";

    // 标记Vosk是否初始化成功
    private boolean voskInitialized = false;

    // 备选默认提供商（当Vosk初始化失败时使用）
    private String fallbackProvider = null;

    /**
     * 应用启动时自动初始化Vosk服务
     */
    // @PostConstruct
    public void initializeDefaultSttService() {
        logger.info("正在初始化默认语音识别服务(Vosk)...");
        initializeVosk();
        if (voskInitialized) {
            logger.info("默认语音识别服务(Vosk)初始化成功，可直接使用");
        } else {
            logger.warn("默认语音识别服务(Vosk)初始化失败，将在需要时尝试使用备选服务");
        }
    }

    /**
     * 初始化Vosk服务
     */
    private synchronized SttService initializeVosk() {
        if (serviceCache.containsKey(DEFAULT_PROVIDER)) {
            return serviceCache.get(DEFAULT_PROVIDER);
        }

        try {
            var voskService = new VoskService();
            voskService.initialize();

            // 检查模型是否真正加载成功
            if (!voskService.isModelLoaded()) {
                throw new Exception("Vosk model was not properly loaded");
            }

            serviceCache.put(DEFAULT_PROVIDER, voskService);
            voskInitialized = true;
            logger.info("Vosk STT服务初始化成功");
            return voskService;
        } catch (Exception e) {
            voskInitialized = false;
        }
        return null;
    }

    /**
     * 获取默认STT服务
     */
    public SttService getDefaultSttService() {
        return getSttService(null);
    }

    /**
     * 根据配置获取STT服务
     */
    public SttService getSttService(SysConfig config) {
        if (config == null) {
            config = new SysConfig().setProvider(DEFAULT_PROVIDER);
            config.setId(-1);
        }

        // 对于API服务，使用"provider:configId"作为缓存键，确保每个配置使用独立的服务实例
        var cacheKey = config.getProvider() + ":" + config.getId();

        // 检查是否已有该配置的服务实例
        if (serviceCache.containsKey(cacheKey)) {
            return serviceCache.get(cacheKey);
        }

        // 创建新的API服务实例
        var service = createApiService(config);
        serviceCache.put(cacheKey, service);

        // 如果没有备选默认服务，将此服务设为备选
        if (fallbackProvider == null) {
            fallbackProvider = cacheKey;
        }

        return service;
    }

    /**
     * 根据配置创建API类型的STT服务
     */
    private SttService createApiService(@Nonnull SysConfig config) {
        return switch (config.getProvider()) {
            case "tencent" -> new TencentService(config);
            case "aliyun" -> new AliyunService(config);
            case "funasr" -> new FunASRService(config);
            case "xfyun" -> new XfyunService(config);
            case "doubao" -> new DoubaoService(config);
            case "youdao" -> new YoudaoService(config);
            case "azure" -> new AzureSttService(config);
            default -> {
                var service = initializeVosk();
                if (service == null) {
                    // If vosk create failed, return fallback stt service
                    if (fallbackProvider != null && serviceCache.containsKey(fallbackProvider)) {
                        yield serviceCache.get(fallbackProvider);
                    }
                    throw new RuntimeException("Create vosk service failed");
                }
                yield service;
            }
        };
    }

    public void removeCache(SysConfig config) {
        // 对于API服务，使用"provider:configId"作为缓存键，确保每个配置使用独立的服务实例
        Integer configId = config.getId();
        String provider = config.getProvider();
        String cacheKey = provider + ":" + (configId != null ? configId : "default");
        serviceCache.remove(cacheKey);
    }
}