package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.service.FileResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("files")
@Tag(name = "文件相关接口", description = "文件相关接口")
public class FileController {

    @Resource
    private FileResourceService fileService;

    @PostMapping
    @Operation(summary = "上传文件")
    public Either<BizError, ?> upload(@Authorized AuthorizedUser user, MultipartFile file) {
        return fileService.upload("xiaozhi", file);
    }

    @GetMapping("{id}")
    @Operation(summary = "获取文件")
    public Either<BizError, ?> detail(@Authorized AuthorizedUser user, @PathVariable Integer id) {
        return fileService.detail("xiaozhi", id);
    }

}
