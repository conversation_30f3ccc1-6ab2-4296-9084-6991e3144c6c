package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.service.ManagerService;
import com.xiaozhi.vo.ManagerUpdateParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/m/user")
@Tag(name = "家长端个人信息", description = "家长端个人信息")
public class ManagerController {

    @Resource
    private ManagerService managerService;

    @GetMapping()
    @Operation(summary = "获取个人信息")
    public Either<BizError, ?> detail(@Authorized AuthorizedUser user) {
        return managerService.detail(user.getId());
    }

    @PutMapping()
    @Operation(summary = "修改个人信息")
    public Either<BizError, ?> update(@Authorized AuthorizedUser user, @RequestBody ManagerUpdateParams params) {
        return managerService.update(user.getId(), params);
    }
}
