package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.TaskService;
import com.xiaozhi.vo.TaskQueryParams;
import com.xiaozhi.vo.TaskUpdateParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/m/tasks")
@Tag(name = "家长端任务管理", description = "家长端任务管理")
public class TaskController {

    @Resource
    private TaskService taskService;


    @GetMapping
    @Operation(summary = "获取任务列表")
    public Resp find(@Authorized AuthorizedUser user, @QueryParam TaskQueryParams params) {
        params.setManagerId(user.getId());
        return Resp.succeed(taskService.findTasks(params));
    }

    @PutMapping("{id}")
    @Operation(summary = "修改任务信息")
    public Either<BizError, ?> update(@Authorized AuthorizedUser user, @PathVariable Integer id, @RequestBody TaskUpdateParams params) {
        return taskService.updateTask(id, params);
    }
}
