package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.SysMessageService;
import com.xiaozhi.vo.DateRange;
import com.xiaozhi.vo.MessageQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/m/chats")
@Tag(name = "", description = "")
public class ChatController {

    @Resource
    private SysMessageService messageService;

    @GetMapping
    @Operation(summary = "查询今日聊天记录")
    public Resp find(@Authorized AuthorizedUser user, @QueryParam MessageQueryParams params) {
        params.setDeviceId(Optional.of(user.getDeviceId()));
        params.setCreatedAt(Optional.of(DateRange.today()));
        params.setOrd(1);
        return messageService.findPage(params);
    }

}
