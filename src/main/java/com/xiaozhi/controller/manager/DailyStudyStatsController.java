package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.DailyStudyStatsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/m/daily-stats")
@Tag(name="家长端统计", description = "家长端统计")
public class DailyStudyStatsController {

    @Resource
    private DailyStudyStatsService dailyStudyStatsService;

    @GetMapping()
    @Operation(summary = "获取学习报告")
    public Resp findStatsOf(@Authorized AuthorizedUser user) {
        return Resp.succeed(dailyStudyStatsService.statsOf(user.getId()));
    }

}
