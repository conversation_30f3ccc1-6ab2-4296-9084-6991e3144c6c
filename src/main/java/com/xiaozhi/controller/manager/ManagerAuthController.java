package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.service.AuthService;
import com.xiaozhi.vo.ManagerLoginParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/m")
@Tag(name = "家长端授权", description = "家长端授权")
public class ManagerAuthController {

    @Resource
    private AuthService authService;

    @PassAuth
    @PostMapping("/login")
    @Operation(summary = "家长登录")
    public Either<BizError, ?> login(@RequestBody ManagerLoginParams params) {
        return authService.loginManagerSms(params);
    }

    @PassAuth
    @PostMapping("/sms-code")
    @Operation(summary = "获取验证码")
    public Either<BizError, ?> sendSmsCode(@RequestBody ManagerLoginParams params) {
        return authService.sendSmsCode(params.getMobile());
    }

}
