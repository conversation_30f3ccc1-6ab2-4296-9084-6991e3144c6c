package com.xiaozhi;

import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.server.mqtt.udp.UdpServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GracefulShutdownListener implements ApplicationListener<ContextClosedEvent> {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private UdpServer udpServer;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("开始应用关闭清理流程...");

        try {
            // 1. 关闭所有活跃会话
            closeAllSessions();

            // 2. 等待一小段时间让会话清理完成
            Thread.sleep(1000);

            log.info("应用关闭清理流程完成");

        } catch (Exception e) {
            log.error("应用关闭清理过程中发生错误", e);
        }
    }

    /**
     * 关闭所有活跃会话
     */
    private void closeAllSessions() {
        try {
            log.info("开始关闭所有活跃会话...");
            int closedCount = sessionManager.clearSessions();
            log.info("已关闭 {} 个活跃会话", closedCount);
        } catch (Exception e) {
            log.error("关闭会话时发生错误", e);
        }
    }

}
