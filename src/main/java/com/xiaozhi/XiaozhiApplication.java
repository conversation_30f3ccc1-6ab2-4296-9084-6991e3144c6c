package com.xiaozhi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableCaching
@EnableScheduling
@MapperScan("com.xiaozhi.dao")
@ComponentScan(value = { "com.xiaozhi" })
public class XiaozhiApplication {

    public static void main(String[] args) {
        SpringApplication.run(XiaozhiApplication.class, args);
    }
}