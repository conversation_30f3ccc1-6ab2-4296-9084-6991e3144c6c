package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("file_resources")
public class FileResource extends BaseEntity {

    private String space;
    private String fileName;
    private String fileFormerName;
    private String fileUrl;
    private String fileType;
    private String storageType;

}
