package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaozhi.enums.TaskType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("task_meta")
public class TaskMeta extends BaseEntity {

    private String cronExpr;
    private Integer weekMask;
    private String time;

    private TaskType type;
    private Integer content;
    private String duration;
    private Boolean isEnabled;

    private Integer managerId;

}
