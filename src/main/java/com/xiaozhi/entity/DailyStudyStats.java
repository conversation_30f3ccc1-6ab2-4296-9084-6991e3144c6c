package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Data
@Accessors(chain = true)
@TableName("daily_study_stats")
@EqualsAndHashCode(callSuper = true)
public class DailyStudyStats extends BaseEntity {
    // 总词汇数量
    private int wordCount;
    private String words;
    // 总句型数量
    private int sentenceCount;
    private String sentences;

    // 词汇增加量
    private int wordIncCount;
    // 句型增加量
    private int sentenceIncCount;

    private long speakingTime;
    private long listeningTime;

    private String deviceId;
    private Integer managerId;
    private Integer lastMessageId;

    private LocalDate date;
}
