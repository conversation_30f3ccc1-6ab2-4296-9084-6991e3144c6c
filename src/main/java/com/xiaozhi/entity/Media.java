package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("medias")
public class Media extends BaseEntity {

    private String title;
    private Integer coverId;
    private Integer assetId;
    private String category;
    private Integer ownerId;

}
