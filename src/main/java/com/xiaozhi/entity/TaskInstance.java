package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.enums.TaskType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("task_instances")
public class TaskInstance extends BaseEntity {

    private TaskType type;
    private Integer content;
    private String duration;
    private Integer metaId;
    private String deviceId;
    private Integer managerId;
    private TaskStatus status;
    private LocalDateTime planFireTime;

}
