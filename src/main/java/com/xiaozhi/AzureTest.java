package com.xiaozhi;

import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.ExecutionException;

public class AzureTest {

    public static void main(String[] args) throws ExecutionException, InterruptedException, URISyntaxException {

        //
// For more samples please visit https://github.com/Azure-Samples/cognitive-services-speech-sdk
//

// Creates an instance of a speech config with specified subscription key and service region.
        String subscriptionKey = "undefined";
        String subscriptionRegion = "undefined";

        SpeechConfig config = SpeechConfig.fromEndpoint(new URI("https://southeastasia.tts.speech.microsoft.com"), "1b8HjkGguTG4OFOk8SeWljidFzddhVPGWGLYSU93zjaLKhW2dUwpJQQJ99BHACqBBLyXJ3w3AAAAACOGuoH2");
// Note: the voice setting will not overwrite the voice element in input SSML.
        config.setSpeechSynthesisVoiceName("zh-CN-XiaoshuangNeural");

        String text = """
                <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN"><voice name="zh-CN-XiaoshuangNeural"><prosody pitch="medium" volume="medium">小朋友们，我先问你们一个小问题哈。你们有没有听说过一种神奇的草，它长得有点像小人，还能让人变得更健康呢？<break time="1s"/>嘿嘿，那就是人参啦！<break time="1s"/>人参呀，可是一种特别珍贵的植物。它就像是大自然送给我们的小宝贝。<break time="1s"/>我们可以把人参想象成一个超级厉害的小魔法师。<break time="1s"/>在很久很久以前，<emphasis level="strong">人参的种子就像一颗小小的魔法种子</emphasis>，被风啊、小鸟啊带到了一片神秘的山林里。这片山林就像是一个大大的魔法城堡，里面有肥沃的土壤，就像城堡里的魔法养料，还有充足的阳光和雨露，就像魔法城堡里的魔法药水。<break time="1s"/>人参种子在这片魔法山林里安了家，开始慢慢长大。它的根就像小人的身体，还有一些细细的根须，就像小人的胳膊和腿。<break time="1s"/>人参要长大可不是一件容易的事情哦，它需要很多很多的时间。有时候啊，一棵人参要长上好几年，甚至几十年呢！这就好像小魔法师要经过很多很多年的修炼，才能变得更强大。<break time="1s"/>等人参长大了，就会被人们发现。人们把它挖出来，就像是找到了藏在魔法城堡里的宝藏。<break time="1s"/>那人参有什么厉害的地方呢？它就像一个魔法医生，可以帮助我们的身体变得更健康。<break time="1s"/>如果我们有点小感冒，有点没力气，人参就可以给我们补充能量，让我们的身体快快好起来。<break time="1s"/>不过呀，人参虽然很厉害，但也不能随便吃哦，要在大人的帮助下才行。<break time="1s"/>现在，我要考考你们啦。<break time="1s"/>如果人参是一个小魔法师，那它的魔法种子在魔法山林里长大，最需要的魔法养料是什么呀？<break time="1s"/>好啦，小朋友们，闭上眼睛，想象着那片神秘的魔法山林，还有可爱的人参小人，慢慢进入甜甜的梦乡吧。晚安喽。</prosody></voice></speak>
                """;

        SpeechSynthesizer synthesizer = new SpeechSynthesizer(config, AudioConfig.fromWavFileOutput("audio/story.wav"));
        {
            SpeechSynthesisResult result = synthesizer.SpeakSsmlAsync(text).get();
            if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                System.out.println("Speech synthesized for text [" + text + "]");
            }
            else if (result.getReason() == ResultReason.Canceled) {
                SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails.fromResult(result);
                System.out.println("CANCELED: Reason=" + cancellation.getReason());

                if (cancellation.getReason() == CancellationReason.Error) {
                    System.out.println("CANCELED: ErrorCode=" + cancellation.getErrorCode());
                    System.out.println("CANCELED: ErrorDetails=" + cancellation.getErrorDetails());
                    System.out.println("CANCELED: Did you update the subscription info?");
                }
            }

            result.close();
        }
        synthesizer.close();


    }

}
