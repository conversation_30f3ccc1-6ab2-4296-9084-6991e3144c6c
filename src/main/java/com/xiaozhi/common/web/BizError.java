package com.xiaozhi.common.web;

import lombok.Getter;

/**
 * 业务错误类
 */
@Getter
public class BizError implements ErrorInfo {
    private final int code;
    private final String msg;

    private BizError(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    // 静态工厂方法
    public static BizError of(int code, String msg) {
        return new BizError(code, msg);
    }

    // 常用错误
    public static final BizError Success = new BizError(0, "success");
    public static final BizError BadRequest = new BizError(400, "请求不符合规范");
    public static final BizError Unauthorized = new BizError(401, "用户还未登录,请先登录");
    public static final BizError PermissionDenied = new BizError(403, "权限不足,请联系管理员");
    public static final BizError SystemError = new BizError(500, "系统异常，请联系管理员");
    public static final BizError InvalidToken = new BizError(401, "无效的Token");

    // 常用的业务错误
    public static final BizError UserFrozen = new BizError(10001, "用户已冻结");
    public static final BizError UserNotExists = new BizError(10001, "用户不存在");
    public static final BizError UserPasswordIncorrect = new BizError(10001, "密码错误");

    public static final BizError InvalidActivationCode = new BizError(200001, "无效的激活码");
    public static final BizError NoDefaultRole = new BizError(20000, "尚未配置默认角色");

    public static final BizError VALIDATION_FAILED = new BizError(4001, "数据验证失败");
    public static final BizError ResourceNotFound = new BizError(4004, "资源未找到");
    public static final BizError OPERATION_TIMEOUT = new BizError(5001, "操作超时");

    public static final BizError UploadFailed = new BizError(1500, "文件上传失败");
    public static final BizError TaskConflicted = new BizError(1000, "当前时间已经有任务执行了");
}
