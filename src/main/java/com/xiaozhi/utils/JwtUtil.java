package com.xiaozhi.utils;

import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.enums.AuthType;
import io.jsonwebtoken.Jwt;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import io.vavr.control.Either;
import io.vavr.control.Try;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;

public class JwtUtil {

    public static String authorize(Integer userId, AuthType type, String secret) {
        return Jwts.builder()
                .signWith(Keys.hmacShaKeyFor(secret.getBytes()))
                .claim("id", userId)
                .claim("ut", type.getValue())
                .setExpiration(Date.from(Instant.now().plus(Duration.ofDays(7))))
                .compact();
    }

    public static Either<BizError, AuthorizedUser> verify(String token, String secret) {
        return Try.of(() -> Jwts.parserBuilder()
                        .setSigningKey(Keys.hmacShaKeyFor(secret.getBytes()))
                        .build()
                        .parseClaimsJws(token))
                .toEither(BizError.InvalidToken)
                .map(Jwt::getBody)
                .map(claims -> {
                    var type = claims.get("ut", Integer.class);
                    var userId = claims.get("id", Integer.class);
                    return new AuthorizedUser().setId(userId).setType(AuthType.of(type));
                });
    }
}