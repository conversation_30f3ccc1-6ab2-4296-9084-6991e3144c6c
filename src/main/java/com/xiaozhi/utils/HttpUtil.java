package com.xiaozhi.utils;

import io.vavr.control.Try;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class HttpUtil {

    private static RestTemplate _restTemplate;

    @Autowired
    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        _restTemplate = restTemplate;
    }

    public static <T> Try<T> get(String url, Class<T> respType) {
        return Try.ofSupplier(() -> _restTemplate.getForEntity(url, respType))
                .filter(res -> res.getStatusCode() == HttpStatus.OK && res.getBody() != null)
                .map(HttpEntity::getBody);
    }

    public static <R, T> Try<T> post(String url, R body, Class<T> respType) {
        return Try.ofSupplier(() -> _restTemplate.postForEntity(url, new HttpEntity<>(body), respType))
                .filter(res -> res.getStatusCode() == HttpStatus.OK && res.getBody() != null)
                .map(HttpEntity::getBody);
    }

    public static <R, T> Try<T> post(String url, R body, ParameterizedTypeReference<T> respType) {
        return Try.ofSupplier(() -> _restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(body), respType))
                .filter(res -> res.getStatusCode() == HttpStatus.OK && res.getBody() != null)
                .map(HttpEntity::getBody);
    }

    public static <R, T> Try<T> post(String url, HttpHeaders headers, R body, Class<T> respType) {
        return Try.ofSupplier(() -> _restTemplate.postForEntity(url, new HttpEntity<>(body, headers), respType))
                .filter(res -> res.getStatusCode() == HttpStatus.OK && res.getBody() != null)
                .map(HttpEntity::getBody);
    }

    public static <R, T> Try<T> post(String url, HttpHeaders headers, R body, ParameterizedTypeReference<T> respType) {
        return Try.ofSupplier(() -> _restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(body, headers), respType))
                .filter(res -> res.getStatusCode() == HttpStatus.OK && res.getBody() != null)
                .map(HttpEntity::getBody);
    }

}
