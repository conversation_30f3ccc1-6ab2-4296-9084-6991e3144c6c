package com.xiaozhi.utils;

import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.dialogue.llm.providers.CozeChatModel;

import java.util.Map;

public class CozeUtil {

    public static String genStartSentence(StartParams params) {
        var chatModel = new CozeChatModel("7532759572071792674", "sat_DKYZIpfVz7NPw3i3U88msfgwWye4uZbtCn4M1tZkC94nIG9wIVnKjtbdiUA3Yzbq");
        if (params.getConversationId() == null) {
            var convId = chatModel.getConversationId(null);
            params.setConversationId(convId);
        }
        return chatModel.callWithVars(params.getScene(), params.getConversationId(), Map.of(
                "nickname", params.getName(),
                "level", params.getCefr()
        ));
    }

    public static String getStarterConvId() {
        var chatModel = new CozeChatModel("7532759572071792674", "sat_DKYZIpfVz7NPw3i3U88msfgwWye4uZbtCn4M1tZkC94nIG9wIVnKjtbdiUA3Yzbq");
        return chatModel.getConversationId(null);
    }

}
