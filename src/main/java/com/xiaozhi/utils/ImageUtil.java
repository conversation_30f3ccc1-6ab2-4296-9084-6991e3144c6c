package com.xiaozhi.utils;

import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesis;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisListResult;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisParam;
import com.alibaba.dashscope.aigc.imagesynthesis.ImageSynthesisResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.task.AsyncTaskListParam;
import io.vavr.control.Try;
import org.springframework.stereotype.Component;

@Component
public class ImageUtil {

    public static Try<String> generate(String prompt, String size) {
        var param = ImageSynthesisParam.builder()
                        .apiKey("sk-524899872860401da9a7222baeea1803")
                        .model("wan2.2-t2i-flash")
                        .prompt(prompt)
                        .n(1)
                        .size(size)
                        .build();

        return Try.of(() -> new ImageSynthesis().call(param))
                .map(res -> res.getOutput().getResults()
                        .stream()
                        .findFirst()
                        .map(it -> it.get("url"))
                        .orElse(""));
    }

    public static void listTask() throws ApiException, NoApiKeyException {
        ImageSynthesis is = new ImageSynthesis();
        AsyncTaskListParam param = AsyncTaskListParam.builder().build();
        ImageSynthesisListResult result = is.list(param);
        System.out.println(result);
    }

    public void fetchTask() throws ApiException, NoApiKeyException {
        String taskId = "your task id";
        ImageSynthesis is = new ImageSynthesis();
        // If set DASHSCOPE_API_KEY environment variable, apiKey can null.
        ImageSynthesisResult result = is.fetch(taskId, null);
        System.out.println(result.getOutput());
        System.out.println(result.getUsage());
    }

}
