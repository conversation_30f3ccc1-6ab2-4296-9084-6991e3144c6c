package com.xiaozhi.utils;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

public class DateUtils {

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String dayOfMonthStart() {
        var som = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        return dtf.format(som);
    }

    public static String dayOfMonthEnd() {
        var eom = YearMonth.now().atEndOfMonth().atTime(23,59,59);
        return dtf.format(eom);
    }


    public static String buildCron(String time, int weekMask){
        var hm = time.split(":");
        var days = new ArrayList<String>();
        if ((weekMask & 1) != 0) days.add("SUN");
        if ((weekMask & 2) != 0) days.add("MON");
        if ((weekMask & 4) != 0) days.add("TUE");
        if ((weekMask & 8) != 0) days.add("WED");
        if ((weekMask & 16) != 0) days.add("THU");
        if ((weekMask & 32) != 0) days.add("FRI");
        if ((weekMask & 64) != 0) days.add("SAT");

        var dow = days.isEmpty() ? "*" : String.join(",", days);
        return String.format("0 %s %s ? * %s", hm[1], hm[0], dow);
    }
}