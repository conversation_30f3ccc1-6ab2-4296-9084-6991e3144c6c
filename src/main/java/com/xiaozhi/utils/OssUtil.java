package com.xiaozhi.utils;

import com.aliyun.oss.OSS;
import com.xiaozhi.config.OssConfig;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;

@Component
public class OssUtil {

    @Resource
    private OSS oss;

    @Resource
    private OssConfig ossConfig;

    public Try<?> upload(String object, String content) {
        return Try.of(() -> oss.putObject(ossConfig.getBucket(), object, new ByteArrayInputStream(content.getBytes())));
    }

    public Try<?> upload(String object, InputStream inputStream) {
        return Try.of(() -> oss.putObject(ossConfig.getBucket(), object, inputStream));
    }

    public Try<String> sign(String object, int minutes) {
        var calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, minutes);
        return Try.of(() -> oss.generatePresignedUrl(ossConfig.getBucket(), object, calendar.getTime()))
                .map(URL::toString);
    }

}
