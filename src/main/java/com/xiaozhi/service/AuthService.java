package com.xiaozhi.service;

import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.vo.ManagerLoginParams;
import com.xiaozhi.vo.UserLoginParams;
import io.vavr.control.Either;

public interface AuthService {
    Either<BizError, ?> login(UserLoginParams params);

    Either<BizError, AuthorizedUser> verify(String token);

    Either<BizError, ?> loginManager(ManagerLoginParams params);

    Either<BizError, ?> loginManagerSms(ManagerLoginParams params);

    Either<BizError, AuthorizedUser> verifyManager(String token);

    Either<BizError, AuthorizedUser> verifyDevice(String token);

    Either<BizError,?> sendSmsCode(String mobile);
}
