package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.vo.TaskCreateParams;
import com.xiaozhi.vo.TaskQueryParams;
import com.xiaozhi.vo.TaskUpdateParams;
import io.vavr.control.Either;

import java.util.List;

public interface TaskService {

    List<?> findTasks(TaskQueryParams params);

    Either<BizError, ?> createTask(TaskCreateParams params);

    Either<BizError, ?> updateTask(Integer id, TaskUpdateParams params);

}
