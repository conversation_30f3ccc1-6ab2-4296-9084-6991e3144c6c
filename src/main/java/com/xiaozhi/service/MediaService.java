package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.vo.MediaCreateParams;
import com.xiaozhi.vo.MediaQueryParams;
import io.vavr.control.Either;

public interface MediaService {

    Resp find4device(MediaQueryParams params);

    Resp findPage(MediaQueryParams params);

    Either<BizError, ?> create(MediaCreateParams params);

    Either<BizError, ?> delete(Integer id);
}
