package com.xiaozhi.service;

import com.xiaozhi.config.WechatConfig;
import com.xiaozhi.utils.HttpUtil;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;

@Service
public class WechatService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private WechatConfig wechatConfig;

    public Try<String> getUserMobile(String code) {
        return getAccessToken()
                .flatMap(
                    token -> HttpUtil.post(STR."https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=\{token}", Map.of("code", code), UserMobileResp.class)
                        .filter(res -> res.errcode == 0)
                        .map(it -> it.getPhoneInfo().purePhoneNumber)
                );
    }

    private Try<String> getAccessToken() {
        var key = STR."xiaozhi:wechat:\{wechatConfig.getAppId()}:token";
        var url = STR."https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=\{wechatConfig.getAppId()}&secret=\{wechatConfig.getSecret()}";

        return Try.ofSupplier(() -> stringRedisTemplate.opsForValue().get(key))
                .filter(String::isEmpty)
                .flatMap(_ -> HttpUtil.get(url, AccessTokenResp.class)
                        .map(res -> {
                            stringRedisTemplate.opsForValue().set(key, res.accessToken, Duration.ofSeconds(res.expiresIn - 200));
                            return res.accessToken;
                        }));
    }

    @Data
    static class WechatResp {
        private int errcode;
        private String errmsg;
    }

    @Data
    static class UserMobileResp {
        private int errcode;
        private String errmsg;
        private PhoneInfo phoneInfo;
    }

    @Data
    static class PhoneInfo {
        // 用户绑定的手机号（国外手机号会有区号）
        private String phoneNumber;
        // 没有区号的手机号
        private String purePhoneNumber;
        // 区号
        private String countryCode;
    }

    record AccessTokenResp(String accessToken, int expiresIn) {
    }
}
