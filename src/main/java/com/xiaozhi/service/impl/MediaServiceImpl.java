package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dto.MediaInfo;
import com.xiaozhi.entity.Media;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.vo.MediaCreateParams;
import com.xiaozhi.vo.MediaQueryParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MediaServiceImpl implements MediaService {

    @Resource
    private MediaMapper mediaMapper;

    @Override
    public Resp find4device(MediaQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<MediaInfo>()
                .eq(MediaInfo::getCategory, params.getCategory());
        var page = mediaMapper.findPage(params.toPage(), query);

        return Resp.from(page);
    }

    @Override
    public Resp findPage(MediaQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getCategory, params.getCategory())
                .select(Media::getId, Media::getTitle, Media::getCoverId, Media::getAssetId, Media::getCategory, Media::getCreatedAt)
                .orderByDesc(Media::getId);
        var page = mediaMapper.selectPage(params.toPage(), query);

        var records = page.getRecords()
                .stream()
                .map(it -> it)
                .toList();

        return Resp.of(records, page.getTotal());
    }

    @Override
    public Either<BizError, ?> create(MediaCreateParams params) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getTitle, params.getTitle());

        return Option.when(mediaMapper.selectCount(query) <= 0, () -> new Media()
                        .setTitle(params.getTitle())
                        .setCategory(params.getCategory())
                        .setCoverId(params.getCoverId())
                        .setAssetId(params.getAssetId()))
                .toEither(BizError.UserNotExists)
                .map(it -> {
                    mediaMapper.insert(it);
                    return it.getId();
                });
    }

    @Override
    public Either<BizError, ?> delete(Integer id) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getId, id)
                .select(Media::getId);

        return Option.of(mediaMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .map(it -> {
                    mediaMapper.deleteById(id);
                    return true;
                });
    }
}
