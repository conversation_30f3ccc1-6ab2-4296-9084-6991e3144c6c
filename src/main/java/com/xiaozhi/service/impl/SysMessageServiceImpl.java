package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.MessageMapper;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.service.SysMessageService;
import com.xiaozhi.vo.MessageQueryParams;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysMessageServiceImpl implements SysMessageService {

    @Resource
    private MessageMapper messageMapper;

    @Override
    public Resp findPage(MessageQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<SysMessage>()
                .eq(SysMessage::getDeviceId, params.getDeviceId())
                .between(SysMessage::getCreatedAt, params.getCreatedAt())
                .select(SysMessage::getId, SysMessage::getSender, SysMessage::getContent, SysMessage::getCreatedAt)
                .orderBy(true, params.getOrd() == 1, SysMessage::getId);

        return Resp.from(messageMapper.selectPage(params.toPage(), query));
    }

    @Override
    public List<SysMessage> findOf(String deviceId, String messageType, Integer limit) {
        var query = new OhMyLambdaQueryWrapper<SysMessage>()
                .eq(SysMessage::getDeviceId, deviceId)
                .eq(SysMessage::getType, messageType)
                .last(String.format("limit %d", limit));

        return messageMapper.selectList(query);
    }

    @Override
    public void save(SysMessage message) {
        messageMapper.insert(message);
    }

}