package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import io.vavr.control.Either;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

public interface FileResourceService {
    Either<BizError, Integer> upload(String appId, MultipartFile file);
    Either<BizError, Integer> upload(String appId, String filename, InputStream inputStream);
    Either<BizError, Integer> uploadXlsx(String appId, String filename, InputStream inputStream);
    Either<BizError, String> sign(String object);
    Either<BizError,?> detail(String appId, Integer id);
}
