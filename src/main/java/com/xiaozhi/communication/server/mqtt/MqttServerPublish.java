package com.xiaozhi.communication.server.mqtt;

import com.hivemq.client.mqtt.datatypes.MqttQos;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class MqttServerPublish {

    @Resource
    private Mqtt3AsyncClient mqttClient;


    public void send(String topic, String message) {
        log.info("MQTT 开始发送文本消息 {}", message);
        try {
            mqttClient.publishWith()
                    .topic(topic)
                    .payload(message.getBytes(StandardCharsets.UTF_8))
                    .qos(MqttQos.AT_MOST_ONCE)
                    .retain(false)
                    .send()
                    .whenComplete((_, throwable) -> {
                        if (throwable != null) {
                            log.error("发送文本消息失败 - Message: {}", message, throwable);
                        } else {
                            log.debug("发送文本消息到设备成功 - Topic: {}, Message: {}", topic, message);
                        }
                    });
        } catch (Exception e) {
            log.error("发送文本消息异常 - Message: {}", message, e);
        }
    }

    public void wakeup(String topic) {
        send(topic, "{ \"type\": \"wakeup\" }");
    }

    public void open(String topic, String target, String params) {
        send(topic, STR."""
                {
                    "type": "navigate",
                    "target": "\{target}",
                    "action": "open",
                    "category": "\{params}"
                }
                """);
    }

    public void close(String topic, String target) {
        send(topic, STR."""
                {
                    "type": "navigate",
                    "target": "\{target}",
                    "action": "close"
                }
                """);
    }

}
