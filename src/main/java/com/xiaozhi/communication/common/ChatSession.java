package com.xiaozhi.communication.common;

import com.xiaozhi.communication.domain.*;
import com.xiaozhi.communication.domain.iot.IotDescriptor;
import com.xiaozhi.dialogue.llm.memory.Conversation;
import com.xiaozhi.dialogue.llm.tool.ToolsSessionHolder;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpHolder;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.enums.ListenMode;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import reactor.core.publisher.Sinks;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

import java.nio.file.Path;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public abstract class ChatSession {

    private static final Logger logger = LoggerFactory.getLogger(ChatSession.class);

    /**
     * 当前会话的sessionId
     */
    protected String sessionId;
    /**
     * 设备信息
     */
    protected SysDevice sysDevice;
    /**
     * 设备可用角色列表
     */
    protected List<SysRole> sysRoleList;
    /**
     * 当前活跃角色（缓存，避免频繁查询数据库）
     */
    protected RoleWithConfig currentRole;

    protected TaskChain taskChain;

    /**
     * 一个Session在某个时刻，只有一个活跃的Conversation。
     * 当切换角色时，Conversation应该释放新建。切换角色一般是不频繁的。
     */
    protected Conversation conversation;
    /**
     * 设备iot信息
     */
    protected Map<String, IotDescriptor> iotDescriptors = new HashMap<>();
    /**
     * 当前session的function控制器
     */
    protected ToolsSessionHolder toolsSessionHolder;

    private List<String> additionalMessages = new ArrayList<>();

    /**
     * 当前语音发送完毕后，是否关闭session
     */
    protected boolean closeAfterChat;
    /**
     * 是否正在播放音乐
     */
    protected boolean musicPlaying;
    /**
     * 是否正在说话（音频播放状态）
     */
    protected AtomicBoolean playing;
    /**
     * 设备状态（auto, realTime)
     */
    protected ListenMode mode;
    /**
     * 会话的音频数据流
     */
    protected Sinks.Many<byte[]> audioSinks;
    /**
     * 会话是否正在进行流式识别
     */
    protected boolean streamingState;
    /**
     * 会话的最后有效活动时间
     */
    protected Instant lastActivityTime;

    /**
     * 音频通道是否打开可用
     */
    protected boolean isAudioChannelOpen;

    /**
     * 客户端中断
     */
    protected boolean isClientAbort;

    /**
     * 会话属性存储
     */
    protected final ConcurrentHashMap<String, Object> attributes = new ConcurrentHashMap<>();

    /**
     * 当前对话的音频文件路径列表（按顺序存储）
     */
    private final List<String> audioFiles = new ArrayList<>();

    /**
     * 当前对话的助手回复文本累加器
     */
    private final StringBuilder assistantResponse = new StringBuilder();

    // --------------------设备mcp-------------------------
    private DeviceMcpHolder deviceMcpHolder = new DeviceMcpHolder();

    public ChatSession(String sessionId) {
        this.sessionId = sessionId;
        this.lastActivityTime = Instant.now();
        this.playing = new AtomicBoolean(false);
    }

    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    public Object getAttribute(String key) {
        return attributes.get(key);
    }

    public void setAssistantTimeMillis(Long assistantTimeMillis) {
        setAttribute("assistantTimeMillis", assistantTimeMillis);
    }

    public Long getAssistantTimeMillis() {
        return (Long) getAttribute("assistantTimeMillis");
    }

    public void setUserTimeMillis(Long userTimeMillis) {
        setAttribute("userTimeMillis", userTimeMillis);
    }

    public Long getUserTimeMillis() {
        return (Long) getAttribute("userTimeMillis");
    }

    /**
     * 音频文件约定路径为：audio/{device-id}/{role-id}/{timestamp}-user.wav
     * {device-id}/{role-id}/{timestamp}-user 能确定唯一性，不会有并发的麻烦。
     * 除非多设备在嵌入式软件里强行修改mac地址（deviceId目前是基于mac地址的)
     */
    private Path getAudioPath(String who, Long timeMillis) {

        Instant instant = Instant.ofEpochMilli(timeMillis).truncatedTo(ChronoUnit.SECONDS);

        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        String datetime = localDateTime.format(DateTimeFormatter.ISO_DATE_TIME).replace(":", "");
        SysDevice device = this.getSysDevice();
        // 判断设备ID是否有不适合路径的特殊字符，它很可能是mac地址需要转换。
        String deviceId = device.getDeviceId().replace(":", "-");
        String roleId = device.getRoleId().toString();
        String filename = "%s-%s.wav".formatted(datetime, who);
        return Path.of(AudioUtils.AUDIO_PATH, deviceId, roleId, filename);
    }

    public Path getUserAudioPath() {
        return getAudioPath("user", this.getUserTimeMillis());
    }

    public Path getAssistantAudioPath() {
        return getAudioPath("assistant", getAssistantTimeMillis());
    }

    // ==================== 音频文件管理 ====================

    /**
     * 添加音频文件路径到当前对话
     *
     * @param audioPath 音频文件路径
     */
    public synchronized void addAudioFile(String audioPath) {
        if (audioPath != null && !audioPath.isEmpty()) {
            audioFiles.add(audioPath);
            logger.debug("添加音频文件到会话 - sessionId: {}, audioPath: {}, 总数: {}",
                    sessionId, audioPath, audioFiles.size());
        }
    }

    /**
     * 获取当前对话的所有音频文件路径（按顺序）
     *
     * @return 音频文件路径列表的副本
     */
    public synchronized List<String> getAudioFiles() {
        return new ArrayList<>(audioFiles);
    }

    /**
     * 清空音频文件列表
     */
    public synchronized void clearAudioFiles() {
        audioFiles.clear();
        logger.debug("清空会话音频文件列表 - sessionId: {}", sessionId);
    }

    // ==================== 助手回复文本管理 ====================

    /**
     * 追加助手回复文本
     *
     * @param text 要追加的文本
     */
    public synchronized void appendAssistantResponse(String text) {
        if (text != null && !text.isEmpty()) {
            assistantResponse.append(text);
            logger.debug("追加助手回复文本到会话 - sessionId: {}, text: '{}', 总长度: {}",
                    sessionId, text, assistantResponse.length());
        }
    }

    /**
     * 获取完整的助手回复文本
     *
     * @return 助手回复文本
     */
    public synchronized String getAssistantResponse() {
        return assistantResponse.toString();
    }

    /**
     * 清空助手回复文本
     */
    public synchronized void clearAssistantResponse() {
        assistantResponse.setLength(0);
        logger.debug("清空会话助手回复文本 - sessionId: {}", sessionId);
    }

    /**
     * 重置当前对话的音频和文本数据（通常在新对话开始时调用）
     */
    public synchronized void resetDialogueData() {
        clearAudioFiles();
        clearAssistantResponse();
        logger.debug("重置会话对话数据 - sessionId: {}", sessionId);
    }

    public ToolsSessionHolder getFunctionSessionHolder() {
        return toolsSessionHolder;
    }

    public void setFunctionSessionHolder(ToolsSessionHolder toolsSessionHolder) {
        this.toolsSessionHolder = toolsSessionHolder;
    }

    public List<ToolCallback> getToolCallbacks() {
        return toolsSessionHolder.getAllFunction();
    }

    /**
     * 会话连接是否打开中
     */
    public abstract boolean isOpen();

    public abstract void close();

    public abstract void sendTextMessage(String message);

    public abstract void sendBinaryMessage(byte[] message);

    public void sendTypedMessage(BaseMessage message) {
        this.sendTextMessage(JsonUtil.toJson(message));
    }

    public void sendTTSStart() {
        this.sendTypedMessage(new TtsMessage("start"));
    }

    public void sendSentenceStart(String text) {
        this.sendTypedMessage(new TtsMessage(text, "sentence_start"));
    }

    public void sendTTSStop() {
        this.sendTypedMessage(new TtsMessage("stop"));
    }

    public void sendSttMessage(String text) {
        this.sendTypedMessage(new SttMessage(text));
    }

    public void sendIotCommandMessage(List<Map<String, Object>> commands) {
        this.sendTypedMessage(new IotCommandMessage(getSessionId(), commands));
    }

    public void sendEmotion(String emotion) {
        this.sendTypedMessage(new EmotionMessage(getSessionId(), emotion));
    }

    // ==================== 音频播放状态管理 ====================

    /**
     * 设置音频播放状态
     *
     * @param isPlaying 是否正在播放
     */
    public void setAudioPlaying(boolean isPlaying) {
        this.playing.set(isPlaying);
    }

    /**
     * 获取音频播放状态
     *
     * @return 是否正在播放
     */
    public boolean isAudioPlaying() {
        return this.playing.get();
    }

    /**
     * 原子性地设置播放状态（如果当前状态符合预期）
     *
     * @param expect 期望的当前状态
     * @param update 要设置的新状态
     * @return 是否设置成功
     */
    public boolean compareAndSetAudioPlaying(boolean expect, boolean update) {
        return this.playing.compareAndSet(expect, update);
    }

    /**
     * 获取播放状态的 AtomicBoolean 引用（用于高级操作）
     *
     * @return AtomicBoolean 引用
     */
    public AtomicBoolean getPlayingState() {
        return this.playing;
    }

    // ==================== 兼容性方法 ====================

    /**
     * 设置播放状态（兼容原有接口）
     *
     * @param playing 是否正在播放
     */
    public void setPlaying(boolean playing) {
        setAudioPlaying(playing);
    }

    /**
     * 获取播放状态（兼容原有接口）
     *
     * @return 是否正在播放
     */
    public boolean isPlaying() {
        return isAudioPlaying();
    }

    /**
     * 清除当前角色缓存（角色切换时使用）
     */
    public void clearCurrentRole() {
        this.currentRole = null;
    }

    /**
     * 检查当前角色是否已缓存
     *
     * @return 是否已缓存
     */
    public boolean hasCurrentRole() {
        return currentRole != null;
    }

    public void createAudioStream() {
        audioSinks =  Sinks.many().multicast().onBackpressureBuffer();
    }
}
