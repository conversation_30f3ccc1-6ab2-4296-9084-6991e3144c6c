package com.xiaozhi.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * OTA 接口响应
 */
@Data
@Accessors(chain = true)
public class OtaResponse {
    private Mqtt mqtt;
    private Activation activation;
    private Websocket websocket;
    private ServerTime serverTime;
    private Firmware firmware;

    /**
     * 设备激活信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Activation {
        // 激活码
        private String code;
        // 屏幕显示信息
        private String message;
        // DeviceId
        private String challenge;
    }

    /**
     * MQTT协议服务器配置信息
     */
    @Data
    @Accessors(chain = true)
    public static class Mqtt {
        private String endpoint;
        // GID_test@@@device-id@@@uuid
        private String clientId;
        private String username;
        private String password;
        private String publishTopic;
    }

    /**
     * Websocket协议服务器配置信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Websocket {
        private String url;
        private String token;
    }

    /**
     * 服务器时间信息（用于同步设备时间）
     */
    @Data
    @Accessors(chain = true)
    public static class ServerTime {
        // 当前时间戳
        private long timestamp;
        // 服务器时区
        private String timezone;
        // 服务器时区偏移量
        private int timezoneOffset;

        public static ServerTime SystemDefault() {
            var now = ZonedDateTime.now();
            return new ServerTime()
                    .setTimestamp(now.toInstant().toEpochMilli())
                    .setTimezone(now.getZone().getId())
                    .setTimezoneOffset(now.getOffset().getTotalSeconds() / 60);
        }

        public static ServerTime CST() {
            var now = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
            return new ServerTime()
                    .setTimestamp(now.toInstant().toEpochMilli())
                    .setTimezone(now.getZone().getId())
                    .setTimezoneOffset(now.getOffset().getTotalSeconds() / 60);
        }
    }

    /**
     * 最新版本固件信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Firmware {
        // 固件版本号
        private String version;
        // 固件下载链接（如果有更新）
        private String url;
    }
}