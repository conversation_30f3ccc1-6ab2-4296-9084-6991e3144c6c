# Azure Cognitive Services 集成文档

本文档介绍如何在小智ESP32服务器中集成和使用Azure Cognitive Services的语音服务（TTS和STT）。

## 概述

Azure Cognitive Services Speech SDK 提供了高质量的文本转语音（TTS）和语音转文本（STT）服务。本项目已集成了Microsoft Cognitive Services Speech SDK v1.45.0。

## 功能特性

### Azure TTS服务 (AzureService)
- ✅ 支持文本转语音合成
- ✅ 支持流式TTS（实时音频流输出）
- ✅ 支持语速调节
- ✅ 支持SSML格式
- ✅ 支持多种语音和语言
- ✅ 自动重试机制
- ✅ 输出WAV格式音频文件

### Azure STT服务 (AzureSttService)
- ✅ 支持语音转文本识别
- ✅ 支持流式STT（实时语音识别）
- ✅ 支持连续识别
- ✅ 支持多种语言
- ✅ 自动重试机制
- ✅ 实时识别结果回调

## 配置要求

### Azure订阅和资源
1. 创建Azure账户并获取订阅
2. 在Azure Portal中创建"语音服务"资源
3. 获取以下信息：
   - **订阅密钥** (Subscription Key)
   - **服务区域** (Service Region，如：eastus, westus2等)

### 系统配置

在数据库的`sys_config`表中添加Azure配置：

#### TTS配置示例
```sql
INSERT INTO sys_config (provider, api_key, api_secret, api_url, app_id, created_at, updated_at) 
VALUES ('azure', 'your-subscription-key', 'your-region', NULL, NULL, NOW(), NOW());
```

#### STT配置示例
```sql
INSERT INTO sys_config (provider, api_key, api_secret, api_url, app_id, created_at, updated_at) 
VALUES ('azure', 'your-subscription-key', 'your-region', 'zh-CN', NULL, NOW(), NOW());
```

### 配置字段说明

| 字段 | TTS用途 | STT用途 | 示例值 |
|------|---------|---------|--------|
| `api_key` | 订阅密钥 | 订阅密钥 | `your-subscription-key` |
| `api_secret` | 服务区域 | 服务区域 | `eastus`, `westus2` |
| `api_url` | 未使用 | 识别语言 | `zh-CN`, `en-US` |

## 使用方法

### TTS服务使用

```java
@Autowired
private TtsServiceFactory ttsServiceFactory;

// 获取Azure TTS服务
SysConfig config = new SysConfig();
config.setProvider("azure");
config.setApiKey("your-subscription-key");
config.setApiSecret("your-region");

TtsService azureService = ttsServiceFactory.getTtsService(config, "zh-CN-XiaoxiaoNeural");

// 文本转语音
Text2SpeechParams params = new Text2SpeechParams("你好，世界！", 1.0);
String audioFilePath = azureService.textToSpeech(params);

// 流式TTS
azureService.streamTextToSpeech("你好，世界！", audioData -> {
    // 处理实时音频数据
    System.out.println("收到音频数据: " + audioData.length + " 字节");
});
```

### STT服务使用

```java
@Autowired
private SttServiceFactory sttServiceFactory;

// 获取Azure STT服务
SysConfig config = new SysConfig();
config.setProvider("azure");
config.setApiKey("your-subscription-key");
config.setApiSecret("your-region");
config.setApiUrl("zh-CN"); // 识别语言

SttService azureService = sttServiceFactory.getSttService(config);

// 单次识别
byte[] audioData = loadAudioFile("audio.wav");
String recognizedText = azureService.recognition(audioData);

// 流式识别
Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
String result = azureService.streamRecognition(audioSink);

// 发送音频数据到流
audioSink.tryEmitNext(audioChunk1);
audioSink.tryEmitNext(audioChunk2);
audioSink.tryEmitComplete();
```

## 支持的语音

### 中文语音示例
- `zh-CN-XiaoxiaoNeural` - 晓晓（女声）
- `zh-CN-YunxiNeural` - 云希（男声）
- `zh-CN-YunyangNeural` - 云扬（男声）
- `zh-CN-XiaochenNeural` - 晓辰（女声）

### 英文语音示例
- `en-US-JennyNeural` - Jenny（女声）
- `en-US-GuyNeural` - Guy（男声）
- `en-US-AriaNeural` - Aria（女声）

更多语音请参考：[Azure TTS语音列表](https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support#text-to-speech)

## 支持的语言

### STT识别语言
- `zh-CN` - 中文（简体）
- `en-US` - 英语（美国）
- `ja-JP` - 日语
- `ko-KR` - 韩语

更多语言请参考：[Azure STT语言支持](https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support#speech-to-text)

## 音频格式要求

### TTS输出格式
- **格式**: WAV
- **采样率**: 16kHz
- **位深**: 16位
- **声道**: 单声道

### STT输入格式
- **格式**: PCM
- **采样率**: 16kHz
- **位深**: 16位
- **声道**: 单声道

## 错误处理

服务包含完善的错误处理机制：

1. **自动重试**: 最多重试3次，每次间隔1秒
2. **超时处理**: STT流式识别30秒超时
3. **详细日志**: 记录所有错误和调试信息
4. **异常传播**: 将Azure SDK异常转换为业务异常

## 性能优化

1. **连接复用**: 使用连接池减少连接开销
2. **流式处理**: 支持实时音频流处理
3. **内存管理**: 及时释放音频资源
4. **并发支持**: 支持多线程并发调用

## 故障排除

### 常见问题

1. **认证失败**
   - 检查订阅密钥是否正确
   - 确认服务区域设置正确

2. **网络连接问题**
   - 确保服务器可以访问Azure服务
   - 检查防火墙设置

3. **音频格式问题**
   - 确保音频格式符合要求
   - 检查采样率和位深设置

4. **语音/语言不支持**
   - 确认使用的语音名称正确
   - 检查语言代码格式

### 日志级别

建议在开发环境中设置DEBUG级别日志：

```yaml
logging:
  level:
    com.xiaozhi.dialogue.tts.providers.AzureService: DEBUG
    com.xiaozhi.dialogue.stt.providers.AzureSttService: DEBUG
```

## 依赖信息

项目已包含必要的Maven依赖：

```xml
<dependency>
    <groupId>com.microsoft.cognitiveservices.speech</groupId>
    <artifactId>client-sdk</artifactId>
    <version>1.45.0</version>
</dependency>
```

## 参考资料

- [Azure Speech Service 官方文档](https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/)
- [Speech SDK for Java 文档](https://docs.microsoft.com/en-us/java/api/com.microsoft.cognitiveservices.speech)
- [Azure 语音服务定价](https://azure.microsoft.com/en-us/pricing/details/cognitive-services/speech-services/)
