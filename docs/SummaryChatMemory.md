# SummaryChatMemory 实现文档

## 概述

`SummaryChatMemory` 是基于 OpenMemory API 的聊天记忆实现，提供智能记忆管理和摘要生成功能。它实现了 `ChatMemory` 接口，可以替代默认的 `DatabaseChatMemory` 实现。

## 实现状态

✅ **已完成实现**
- ✅ 基于 OpenMemory API 的完整实现
- ✅ 异步消息存储和处理
- ✅ 智能记忆推理和摘要
- ✅ 完善的错误处理和日志记录
- ✅ 配置化启用/禁用功能
- ✅ 与现有 ChatMemory 接口完全兼容

## 功能特性

- **智能记忆存储**: 使用 OpenMemory API 进行智能记忆推理和存储
- **异步处理**: 所有写操作（添加、清除）都是异步执行，不会阻塞主线程
- **元数据支持**: 保存完整的消息上下文信息，包括设备ID、会话ID、角色ID等
- **错误处理**: 完善的错误处理和日志记录
- **配置化**: 支持通过配置文件启用/禁用功能

## 配置

### application.yml 配置

```yaml
# OpenMemory 配置
openmemory:
  base-url: http://localhost:8765  # OpenMemory 服务地址
  enabled: true                    # 是否启用 SummaryChatMemory
  timeout: 30000                   # 请求超时时间（毫秒）
```

### 启用条件

`SummaryChatMemory` 只有在以下条件满足时才会被激活：
- `openmemory.enabled=true` 配置项设置为 true

如果未启用，系统将继续使用默认的 `DatabaseChatMemory` 实现。

## API 映射

### ChatMemory 方法与 OpenMemory API 的映射关系

| ChatMemory 方法 | OpenMemory API | 说明 |
|-----------------|----------------|------|
| `addMessage()` | `POST /api/v1/memories/` | 创建新的记忆条目 |
| `getMessages()` | `GET /api/v1/memories/` | 获取记忆列表 |
| `clearMessages()` | `DELETE /api/v1/memories/` | 删除指定用户的所有记忆 |

## 数据转换

### 消息到记忆的转换

当调用 `addMessage()` 时，系统会：

1. **构建记忆文本**: 根据发送者类型格式化消息内容
   - 用户消息: "用户说: {content}"
   - 助手消息: "助手回复: {content}"
   - 其他: "{sender}: {content}"

2. **添加元数据**: 保存原始消息的所有上下文信息
   ```json
   {
     "deviceId": "device123",
     "sessionId": "session456", 
     "sender": "user",
     "roleId": 1,
     "messageType": "NORMAL",
     "timestamp": 1640995200000
   }
   ```

3. **设置应用标识**: 使用 "xiaozhi" 作为应用标识

### 记忆到消息的转换

当调用 `getMessages()` 时，系统会：

1. 从 OpenMemory 获取记忆列表
2. 从元数据中恢复原始消息信息
3. 转换为 `SysMessage` 对象返回

## 使用示例

### 基本使用

```java
@Autowired
private ChatMemory chatMemory; // 如果启用了 OpenMemory，这里会注入 SummaryChatMemory

// 添加消息
chatMemory.addMessage("device123", "session456", "user", "你好", 1, "NORMAL", System.currentTimeMillis());

// 获取历史消息
List<SysMessage> messages = chatMemory.getMessages("device123", "NORMAL", 10);

// 清除设备记忆
chatMemory.clearMessages("device123");
```

### 在 Conversation 中使用

`SummaryChatMemory` 可以直接替换 `DatabaseChatMemory` 在 `MessageWindowConversation` 中使用：

```java
MessageWindowConversation conversation = MessageWindowConversation.builder()
    .chatMemory(summaryChatMemory) // 使用 SummaryChatMemory
    .maxMessages(10)
    .device(device)
    .role(role)
    .sessionId(sessionId)
    .build();
```

## 性能考虑

- **异步处理**: 写操作不会阻塞主线程
- **网络超时**: 配置了合理的超时时间，避免长时间等待
- **错误恢复**: 网络错误不会影响系统正常运行

## 监控和日志

系统提供了详细的日志记录：

- `DEBUG` 级别: 记录成功的操作和详细信息
- `WARN` 级别: 记录 API 调用失败但不影响系统运行的情况
- `ERROR` 级别: 记录严重错误和异常

## 故障排除

### 常见问题

1. **OpenMemory 服务不可用**
   - 检查 `openmemory.base-url` 配置是否正确
   - 确认 OpenMemory 服务正在运行
   - 查看网络连接是否正常

2. **记忆存储失败**
   - 检查日志中的错误信息
   - 确认 OpenMemory API 返回的错误码
   - 验证请求数据格式是否正确

3. **记忆检索为空**
   - 确认设备ID是否正确
   - 检查 OpenMemory 中是否有对应的记忆数据
   - 验证查询参数是否正确

### 调试建议

1. 启用 DEBUG 日志级别查看详细信息
2. 使用 OpenMemory 的 Web 界面验证数据
3. 检查网络连接和防火墙设置

## 扩展和定制

如需定制 `SummaryChatMemory` 的行为，可以：

1. 修改 `buildMemoryText()` 方法来改变记忆文本格式
2. 调整元数据结构来保存更多上下文信息
3. 实现自定义的错误处理策略
4. 添加缓存机制提高性能
